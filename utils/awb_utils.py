def validate_awb_number(awb_number):
    """
    Validate AWB number format
    Expected format: XXX-XXXXXXXX (3 digits, dash, 8 digits)
    """
    if not awb_number:
        return False

    # Remove any whitespace
    awb_number = awb_number.strip()

    # Check basic format
    if len(awb_number) != 12:
        return False

    if awb_number[3] != "-":
        return False

    # Check if first 3 characters are digits
    if not awb_number[:3].isdigit():
        return False

    # Check if last 8 characters are digits
    if not awb_number[4:].isdigit():
        return False

    return True


def format_awb_number(awb_number):
    """
    Format AWB number to standard format
    """
    if not awb_number:
        return None

    # Remove any non-digit characters except dash
    cleaned = "".join(c for c in awb_number if c.isdigit() or c == "-")

    # If already properly formatted, return as is
    if validate_awb_number(cleaned):
        return cleaned

    # Try to format if it's just digits
    digits_only = "".join(c for c in awb_number if c.isdigit())
    if len(digits_only) == 11:
        return f"{digits_only[:3]}-{digits_only[3:]}"

    return None


def extract_awb_from_filename(filename):
    """
    Extract AWB number from filename
    """
    import re

    # Pattern to match AWB number in filename
    pattern = r"(\d{3}-\d{8})"
    match = re.search(pattern, filename)

    if match:
        return match.group(1)

    # Try to extract digits and format
    digits = re.findall(r"\d+", filename)
    if len(digits) >= 2:
        # Try to combine first two digit groups
        combined = "".join(digits[:2])
        if len(combined) == 11:
            return f"{combined[:3]}-{combined[3:]}"

    return None


def normalize_awb_number(awb_number):
    """
    Normalize AWB number to standard IATA format.

    This function:
    - Removes spaces and non-digit characters except hyphens
    - Handles 11-digit AWB numbers by removing the check digit (last digit)
    - Formats to XXX-XXXXXXX format (3 digits, hyphen, 7 digits)
    - Returns the input unchanged if it doesn't match expected patterns

    Args:
        awb_number (str): AWB number in various formats

    Returns:
        str: Normalized AWB number or original input if normalization fails
    """
    if not awb_number:
        return ""

    # Convert to string and strip whitespace
    awb_str = str(awb_number).strip()

    if not awb_str:
        return ""

    # Remove spaces and non-alphanumeric characters except hyphens
    cleaned = "".join(c for c in awb_str if c.isdigit() or c == "-")

    # If already in correct format (XXX-XXXXXXX), return as is
    if len(cleaned) == 11 and cleaned[3] == "-" and cleaned[:3].isdigit() and cleaned[4:].isdigit():
        return cleaned

    # Extract only digits for processing
    digits_only = "".join(c for c in cleaned if c.isdigit())

    # Handle 11-digit AWB numbers (remove check digit)
    if len(digits_only) == 11:
        # Remove the last digit (check digit) to get 10 digits
        digits_only = digits_only[:10]

    # Format 10-digit AWB number to XXX-XXXXXXX
    if len(digits_only) == 10:
        return f"{digits_only[:3]}-{digits_only[3:]}"

    # If we can't normalize it, return the original input
    return awb_str


def is_valid_awb_format(awb_number):
    """
    Check if AWB number is in a valid format.

    This function is more flexible and accepts various AWB formats:
    - Standard format: XXX-XXXXXXXX
    - Without hyphen: XXXXXXXXXXX
    - With spaces: XXX XXXXXXXX
    - With check digits or other trailing characters

    Args:
        awb_number (str): AWB number to validate

    Returns:
        bool: True if format is valid, False otherwise
    """
    if not awb_number:
        return False

    awb_str = str(awb_number).strip()

    if not awb_str:
        return False

    import re

    # Extract digits from the AWB number
    digits = re.findall(r'\d', awb_str)

    # Check if we have at least 10 digits (minimum for AWB)
    if len(digits) < 10:
        return False

    # Check if first 3 characters (after removing non-digits) form the airline code
    # and the remaining form the serial number
    digits_str = ''.join(digits)

    # Valid AWB should have 10 or 11 digits
    if len(digits_str) < 10 or len(digits_str) > 11:
        return False

    # Check for standard formats
    patterns = [
        r'^\d{3}-\d{7,8}$',       # XXX-XXXXXXX or XXX-XXXXXXXX
        r'^\d{10,11}$',           # XXXXXXXXXX or XXXXXXXXXXX
        r'^\d{3}\s\d{7,8}$',      # XXX XXXXXXX or XXX XXXXXXXX
        r'^\d{10,11}[A-Za-z]?$',  # XXXXXXXXXX with optional letter
    ]

    for pattern in patterns:
        if re.match(pattern, awb_str):
            return True

    # If none of the standard patterns match, check if it contains valid digits
    # This is a fallback for other formats that might be valid
    return len(digits_str) >= 10 and len(digits_str) <= 11


def extract_awb_from_text(text):
    """
    Extract AWB number from text using regex patterns.

    Args:
        text (str): Text to search for AWB numbers

    Returns:
        str or None: First AWB number found, or None if not found
    """
    if not text:
        return None

    import re

    # Pattern for AWB numbers: 3 digits, hyphen, 7-8 digits
    pattern = r"\b(\d{3}-\d{7,8})\b"
    match = re.search(pattern, text)

    if match:
        return match.group(1)

    # Try pattern without hyphen: 10-11 consecutive digits
    pattern = r"\b(\d{10,11})\b"
    match = re.search(pattern, text)

    if match:
        digits = match.group(1)
        # Format as XXX-XXXXXXX
        if len(digits) == 10:
            return f"{digits[:3]}-{digits[3:]}"
        elif len(digits) == 11:
            # Remove check digit and format
            return f"{digits[:3]}-{digits[3:10]}"

    return None


def generate_partial_awb_id(master_awb, sequence=1):
    """
    Generate partial AWB ID
    Format: PWB-{master_awb}-{sequence}
    """
    if not validate_awb_number(master_awb):
        return None

    return f"PWB-{master_awb}-{sequence}"
