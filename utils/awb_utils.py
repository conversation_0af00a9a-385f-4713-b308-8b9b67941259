def validate_awb_number(awb_number):
    """
    Validate AWB number format
    Expected format: XXX-XXXXXXXX (3 digits, dash, 8 digits)
    """
    if not awb_number:
        return False

    # Remove any whitespace
    awb_number = awb_number.strip()

    # Check basic format
    if len(awb_number) != 12:
        return False

    if awb_number[3] != "-":
        return False

    # Check if first 3 characters are digits
    if not awb_number[:3].isdigit():
        return False

    # Check if last 8 characters are digits
    if not awb_number[4:].isdigit():
        return False

    return True


def format_awb_number(awb_number):
    """
    Format AWB number to standard format
    """
    if not awb_number:
        return None

    # Remove any non-digit characters except dash
    cleaned = "".join(c for c in awb_number if c.isdigit() or c == "-")

    # If already properly formatted, return as is
    if validate_awb_number(cleaned):
        return cleaned

    # Try to format if it's just digits
    digits_only = "".join(c for c in awb_number if c.isdigit())
    if len(digits_only) == 11:
        return f"{digits_only[:3]}-{digits_only[3:]}"

    return None


def extract_awb_from_filename(filename):
    """
    Extract AWB number from filename
    """
    import re

    # Pattern to match AWB number in filename
    pattern = r"(\d{3}-\d{8})"
    match = re.search(pattern, filename)

    if match:
        return match.group(1)

    # Try to extract digits and format
    digits = re.findall(r"\d+", filename)
    if len(digits) >= 2:
        # Try to combine first two digit groups
        combined = "".join(digits[:2])
        if len(combined) == 11:
            return f"{combined[:3]}-{combined[3:]}"

    return None


def generate_partial_awb_id(master_awb, sequence=1):
    """
    Generate partial AWB ID
    Format: PWB-{master_awb}-{sequence}
    """
    if not validate_awb_number(master_awb):
        return None

    return f"PWB-{master_awb}-{sequence}"
